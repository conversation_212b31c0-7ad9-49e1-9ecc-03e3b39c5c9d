<?xml version="1.0" encoding="UTF-8"?>
<testsuites>
  <testsuite name="/Users/<USER>/Projects/Clients/FPMP/smp_online/phpunit.xml" tests="38" assertions="178" errors="0" failures="0" skipped="0" time="0.307418">
    <testsuite name="Feature" tests="38" assertions="178" errors="0" failures="0" skipped="0" time="0.307418">
      <testsuite name="Tests\Feature\LogoutErrorFixTest" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/LogoutErrorFixTest.php" tests="12" assertions="41" errors="0" failures="0" skipped="0" time="0.120556">
        <testcase name="user_can_logout_successfully" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/LogoutErrorFixTest.php" line="28" class="Tests\Feature\LogoutErrorFixTest" classname="Tests.Feature.LogoutErrorFixTest" assertions="5" time="0.070018"/>
        <testcase name="logout_via_header_dropdown_works" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/LogoutErrorFixTest.php" line="48" class="Tests\Feature\LogoutErrorFixTest" classname="Tests.Feature.LogoutErrorFixTest" assertions="5" time="0.008517"/>
        <testcase name="logout_via_sidebar_works" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/LogoutErrorFixTest.php" line="69" class="Tests\Feature\LogoutErrorFixTest" classname="Tests.Feature.LogoutErrorFixTest" assertions="5" time="0.007194"/>
        <testcase name="header_displays_correctly_during_logout_process" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/LogoutErrorFixTest.php" line="90" class="Tests\Feature\LogoutErrorFixTest" classname="Tests.Feature.LogoutErrorFixTest" assertions="3" time="0.005485"/>
        <testcase name="dashboard_pages_handle_null_user_gracefully" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/LogoutErrorFixTest.php" line="104" class="Tests\Feature\LogoutErrorFixTest" classname="Tests.Feature.LogoutErrorFixTest" assertions="3" time="0.004078"/>
        <testcase name="admin_dashboard_handles_null_user_gracefully" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/LogoutErrorFixTest.php" line="115" class="Tests\Feature\LogoutErrorFixTest" classname="Tests.Feature.LogoutErrorFixTest" assertions="3" time="0.004212"/>
        <testcase name="client_dashboard_handles_null_user_gracefully" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/LogoutErrorFixTest.php" line="126" class="Tests\Feature\LogoutErrorFixTest" classname="Tests.Feature.LogoutErrorFixTest" assertions="3" time="0.003371"/>
        <testcase name="main_dashboard_route_handles_null_user_gracefully" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/LogoutErrorFixTest.php" line="137" class="Tests\Feature\LogoutErrorFixTest" classname="Tests.Feature.LogoutErrorFixTest" assertions="3" time="0.003321"/>
        <testcase name="header_shows_fallback_values_when_user_is_null" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/LogoutErrorFixTest.php" line="148" class="Tests\Feature\LogoutErrorFixTest" classname="Tests.Feature.LogoutErrorFixTest" assertions="1" time="0.003061"/>
        <testcase name="multiple_logout_attempts_dont_cause_errors" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/LogoutErrorFixTest.php" line="158" class="Tests\Feature\LogoutErrorFixTest" classname="Tests.Feature.LogoutErrorFixTest" assertions="4" time="0.004220"/>
        <testcase name="logout_clears_session_properly" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/LogoutErrorFixTest.php" line="177" class="Tests\Feature\LogoutErrorFixTest" classname="Tests.Feature.LogoutErrorFixTest" assertions="4" time="0.003536"/>
        <testcase name="logout_regenerates_csrf_token" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/LogoutErrorFixTest.php" line="198" class="Tests\Feature\LogoutErrorFixTest" classname="Tests.Feature.LogoutErrorFixTest" assertions="2" time="0.003543"/>
      </testsuite>
      <testsuite name="Tests\Feature\LogoutNullSafetyTest" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/LogoutNullSafetyTest.php" tests="14" assertions="66" errors="0" failures="0" skipped="0" time="0.091608">
        <testcase name="client_user_can_logout_without_null_reference_error" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/LogoutNullSafetyTest.php" line="44" class="Tests\Feature\LogoutNullSafetyTest" classname="Tests.Feature.LogoutNullSafetyTest" assertions="6" time="0.008547"/>
        <testcase name="normal_user_can_logout_without_null_reference_error" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/LogoutNullSafetyTest.php" line="68" class="Tests\Feature\LogoutNullSafetyTest" classname="Tests.Feature.LogoutNullSafetyTest" assertions="6" time="0.006303"/>
        <testcase name="admin_user_can_logout_without_null_reference_error" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/LogoutNullSafetyTest.php" line="92" class="Tests\Feature\LogoutNullSafetyTest" classname="Tests.Feature.LogoutNullSafetyTest" assertions="6" time="0.006595"/>
        <testcase name="header_template_handles_null_user_during_logout_gracefully" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/LogoutNullSafetyTest.php" line="116" class="Tests\Feature\LogoutNullSafetyTest" classname="Tests.Feature.LogoutNullSafetyTest" assertions="4" time="0.007113"/>
        <testcase name="sidebar_template_handles_null_user_during_logout_gracefully" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/LogoutNullSafetyTest.php" line="135" class="Tests\Feature\LogoutNullSafetyTest" classname="Tests.Feature.LogoutNullSafetyTest" assertions="3" time="0.006404"/>
        <testcase name="client_user_sees_clean_header_without_reservation_buttons" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/LogoutNullSafetyTest.php" line="151" class="Tests\Feature\LogoutNullSafetyTest" classname="Tests.Feature.LogoutNullSafetyTest" assertions="5" time="0.007918"/>
        <testcase name="normal_user_sees_reservation_buttons_in_header" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/LogoutNullSafetyTest.php" line="170" class="Tests\Feature\LogoutNullSafetyTest" classname="Tests.Feature.LogoutNullSafetyTest" assertions="4" time="0.005820"/>
        <testcase name="admin_user_sees_reservation_buttons_in_header" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/LogoutNullSafetyTest.php" line="186" class="Tests\Feature\LogoutNullSafetyTest" classname="Tests.Feature.LogoutNullSafetyTest" assertions="4" time="0.005865"/>
        <testcase name="client_user_sees_clean_sidebar_without_fpmp_features" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/LogoutNullSafetyTest.php" line="202" class="Tests\Feature\LogoutNullSafetyTest" classname="Tests.Feature.LogoutNullSafetyTest" assertions="7" time="0.005728"/>
        <testcase name="normal_user_sees_fpmp_features_in_sidebar" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/LogoutNullSafetyTest.php" line="225" class="Tests\Feature\LogoutNullSafetyTest" classname="Tests.Feature.LogoutNullSafetyTest" assertions="4" time="0.005463"/>
        <testcase name="admin_user_sees_all_features_in_sidebar" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/LogoutNullSafetyTest.php" line="243" class="Tests\Feature\LogoutNullSafetyTest" classname="Tests.Feature.LogoutNullSafetyTest" assertions="5" time="0.005878"/>
        <testcase name="multiple_logout_attempts_dont_cause_null_reference_errors" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/LogoutNullSafetyTest.php" line="262" class="Tests\Feature\LogoutNullSafetyTest" classname="Tests.Feature.LogoutNullSafetyTest" assertions="4" time="0.004334"/>
        <testcase name="header_template_null_safe_operators_work_correctly" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/LogoutNullSafetyTest.php" line="281" class="Tests\Feature\LogoutNullSafetyTest" classname="Tests.Feature.LogoutNullSafetyTest" assertions="4" time="0.007442"/>
        <testcase name="sidebar_template_null_safe_operators_work_correctly" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/LogoutNullSafetyTest.php" line="303" class="Tests\Feature\LogoutNullSafetyTest" classname="Tests.Feature.LogoutNullSafetyTest" assertions="4" time="0.008199"/>
      </testsuite>
      <testsuite name="Tests\Feature\LogoutUserNameDisplayTest" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/LogoutUserNameDisplayTest.php" tests="12" assertions="71" errors="0" failures="0" skipped="0" time="0.095254">
        <testcase name="client_user_name_displays_correctly_before_logout" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/LogoutUserNameDisplayTest.php" line="44" class="Tests\Feature\LogoutUserNameDisplayTest" classname="Tests.Feature.LogoutUserNameDisplayTest" assertions="4" time="0.008503"/>
        <testcase name="normal_user_name_displays_correctly_before_logout" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/LogoutUserNameDisplayTest.php" line="62" class="Tests\Feature\LogoutUserNameDisplayTest" classname="Tests.Feature.LogoutUserNameDisplayTest" assertions="4" time="0.006722"/>
        <testcase name="admin_user_name_displays_correctly_before_logout" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/LogoutUserNameDisplayTest.php" line="80" class="Tests\Feature\LogoutUserNameDisplayTest" classname="Tests.Feature.LogoutUserNameDisplayTest" assertions="4" time="0.005867"/>
        <testcase name="header_shows_actual_user_name_not_guest_for_all_user_types" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/LogoutUserNameDisplayTest.php" line="98" class="Tests\Feature\LogoutUserNameDisplayTest" classname="Tests.Feature.LogoutUserNameDisplayTest" assertions="9" time="0.011812"/>
        <testcase name="user_account_information_shows_actual_data_not_fallbacks" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/LogoutUserNameDisplayTest.php" line="123" class="Tests\Feature\LogoutUserNameDisplayTest" classname="Tests.Feature.LogoutUserNameDisplayTest" assertions="6" time="0.007913"/>
        <testcase name="logout_process_completes_successfully_with_proper_user_name_display" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/LogoutUserNameDisplayTest.php" line="145" class="Tests\Feature\LogoutUserNameDisplayTest" classname="Tests.Feature.LogoutUserNameDisplayTest" assertions="6" time="0.006281"/>
        <testcase name="role_based_visibility_still_works_with_corrected_user_name_display" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/LogoutUserNameDisplayTest.php" line="165" class="Tests\Feature\LogoutUserNameDisplayTest" classname="Tests.Feature.LogoutUserNameDisplayTest" assertions="8" time="0.007654"/>
        <testcase name="null_safe_operators_still_work_for_method_calls" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/LogoutUserNameDisplayTest.php" line="189" class="Tests\Feature\LogoutUserNameDisplayTest" classname="Tests.Feature.LogoutUserNameDisplayTest" assertions="4" time="0.006316"/>
        <testcase name="fallback_values_only_show_when_user_is_actually_null" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/LogoutUserNameDisplayTest.php" line="208" class="Tests\Feature\LogoutUserNameDisplayTest" classname="Tests.Feature.LogoutUserNameDisplayTest" assertions="3" time="0.003962"/>
        <testcase name="user_properties_vs_method_calls_handled_correctly" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/LogoutUserNameDisplayTest.php" line="219" class="Tests\Feature\LogoutUserNameDisplayTest" classname="Tests.Feature.LogoutUserNameDisplayTest" assertions="5" time="0.005226"/>
        <testcase name="all_user_types_can_logout_with_correct_name_display" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/LogoutUserNameDisplayTest.php" line="239" class="Tests\Feature\LogoutUserNameDisplayTest" classname="Tests.Feature.LogoutUserNameDisplayTest" assertions="9" time="0.011042"/>
        <testcase name="header_template_shows_correct_user_info_for_all_roles" file="/Users/<USER>/Projects/Clients/FPMP/smp_online/tests/Feature/LogoutUserNameDisplayTest.php" line="270" class="Tests\Feature\LogoutUserNameDisplayTest" classname="Tests.Feature.LogoutUserNameDisplayTest" assertions="9" time="0.013955"/>
      </testsuite>
    </testsuite>
  </testsuite>
</testsuites>
